<script lang="ts">
    import BreadCrumbs from "$lib/common/components/admin/BreadCrumbs.svelte";
    import FormHeader from "$lib/common/components/admin/FormHeader.svelte";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import { CloseButton, Input, Label } from "flowbite-svelte";
    import type {
        IPurchaseOrder,
        IPurchaseOrderItems,
        PurchaseItemDetails,
    } from "../models/IPurchaseOrder";
    import { getEmptyPurchaseOrderItems } from "../utils/purchase-order-utils";
    import SupplierSearch from "$lib/supplier/components/SupplierSearch.svelte";
    import type { ISupplier } from "$lib/supplier/models/ISupplier";
    import SupplierRawMaterialSearch from "$lib/purchase_invoice/components/SupplierRawMaterialSearch.svelte";
    import {
        capitalizeFirstWord,
        formatDateToInput,
        formatDateUI,
        showErrorToast,
        showSuccessToast,
    } from "$lib/common/utils/common-utils";
    import CustomButton from "$lib/common/components/admin/CustomButton.svelte";
    import { RAW_MATERIAL_STAUS, type IRawMaterial } from "$lib/raw_material/models/IRawMaterial";
    import { RepoProvider } from "$lib/RepoProvider";
    import { goto } from "$app/navigation";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { onMount } from "svelte";

    import jsPDF from "jspdf";
    import { loggedInUser } from "$lib/common/utils/store";
    import { LOGO_URL } from "$lib/common/models/constants";
    import DepartmentSearch from "$lib/department/components/DepartmentSearch.svelte";
    import type { IDepartment } from "$lib/department/models/IDepartment";

    export let isEditCase: boolean = false;
    export let formData: IPurchaseOrder = getEmptyPurchaseOrderItems();

    let selectedSupplier: ISupplier | null = null;
    let selectedFromDepartment: IDepartment | null = null;
    let selectedToDepartment: IDepartment | null = null;
    let selectedItemsMap: Map<string, IPurchaseOrderItems> = new Map();
    let isLoading: boolean = true;
    let oldRawMaterialsIds: number[] = [];
    let validationErrors: Map<string, string> = new Map();

    const handleTotalQty = (e: any, index: number) => {
        try {
            if (e.target.value === "") {
                formData.items[index].qty = 0;
                return;
            }
            formData.items[index].qty = parseFloat(e.target.value);
            console.log(formData.items[index].qty);
            let price = 1;
            let data = selectedItemsMap.get(index.toString());
        } catch (error) {
            formData.items[index].qty = 0;
        }
    };
    const onRawMaterialSelected = (
        data: IRawMaterial,
        item: PurchaseItemDetails,
        index: number
    ) => {
        if (data) {
            if (oldRawMaterialsIds.includes(data.id)) {
                showErrorToast(data.name + " is already added");
                return;
            }

            const gstPercentage = data.gstPercentage;

            item.item = data;
            selectedItemsMap.set(index.toString(), {
                id: data.id,
                qty: 0,
                name: data.name,
            });
        } else {
            oldRawMaterialsIds = oldRawMaterialsIds.filter((id) => id !== item.item.id);

            selectedItemsMap.delete(index.toString());
            formData.items.splice(index, 1);
            // Recreate selected item map
            selectedItemsMap = new Map();
            formData.items.forEach((item, idx) => {
                selectedItemsMap.set(idx.toString(), {
                    id: item.item.id,
                    qty: item.qty,
                    name: item.item.name,
                });
            });
        }

        formData = formData;
        selectedItemsMap = selectedItemsMap;
        console.log(selectedItemsMap, "selecteditem map");
        console.log(formData, "formData");
    };
    const getSelected = (index: number) => {
        console.log(index, "iii");
        console.log(selectedItemsMap, "sss");
        const item = selectedItemsMap.get(index.toString());
        console.log(item);
        return item
            ? ({
                  id: item.id,
                  qty: item.qty,
                  name: item.name,
              } as unknown as IRawMaterial)
            : null;
    };

    const onSubmitHandler = async () => {
        isLoading = true;
        validationErrors = PresenterProvider.purchaseOrderPresenter.onValidate(formData);
        if (validationErrors.size !== 0) {
            showErrorToast(validationErrors.get(validationErrors.keys().next().value!));
            isLoading = false;
            return;
        }
        if (!isEditCase) {
            const res = await RepoProvider.purchaseOrderRepo.create(formData);
            if (res.success) {
                showSuccessToast("Demand Slip Saved Successfully!");
                await goto("/admin/purchase-orders");
            } else {
                showErrorToast(res.message);
            }
        } else {
            const res = await RepoProvider.purchaseOrderRepo.update(formData);
            if (res.success) {
                showSuccessToast("Demand Slip Updated Successfully!");
                await goto("/admin/purchase-orders");
            } else {
                showErrorToast(res.message);
            }
        }
        isLoading = false;
    };

    $: {
        selectedItemsMap;
    }

    onMount(() => {
        console.log(formData.supplier);
        if (formData.supplier.id > 0) {
            selectedSupplier = formData.supplier;
            if (formData.id) {
                formData.id = formData.id;
                if (formData.id > 0) {
                    formData.expectedDate = new Date(formData.expectedDate);
                }
            }
        }

        // Initialize department selections
        if (formData.fromDepartment) {
            selectedFromDepartment = formData.fromDepartment;
        }
        if (formData.toDepartment) {
            selectedToDepartment = formData.toDepartment;
        }

        let index = 0;
        for (const item of formData.items) {
            oldRawMaterialsIds.push(item.item.id);

            selectedItemsMap.set(index.toString(), {
                id: item.item.id,
                qty: item.qty,
                name: item.item.name,
            });

            index++;
        }
        isLoading = false;
    });

    const generatePO = (id: number) => {
        const doc = new jsPDF({
            orientation: "portrait",
            unit: "pt",
            format: "a4",
        });

        const contentWidth = 10.5 * 28.8465; // ≈ 297.64 pt
        const contentHeight = 14.5 * 28.8465;
        const pageWidth = doc.internal.pageSize.getWidth();
        const pageHeight = doc.internal.pageSize.getHeight();

        // Horizontally centered, top-aligned
        const originX = (pageWidth - contentWidth) / 2; // Centered horizontally
        const originY = 10; // Small top margin

        let x = originX + 5;
        let y = originY + 5;
        const lineHeight = 16;

        // Outer border
        doc.setDrawColor(0);
        doc.setLineWidth(1);
        doc.rect(originX, originY, contentWidth, contentHeight);

        // Created By
        doc.setFontSize(8);
        doc.text(
            "Created By: " + formData.createdByName,
            originX + contentWidth - 5,
            originY + 10,
            {
                align: "right",
            }
        );

        // Logo
        doc.addImage(LOGO_URL, "PNG", originX + 0, 0, 90, 70);
        doc.setFont("helvetica", "bold");
        doc.setFontSize(13);
        doc.text("Demand Slip", pageWidth / 2, originY + 50, {
            align: "center",
        });
        doc.setFontSize(10);
        doc.setFont("helvetica", "normal");

        y += 80;

        // Sr. No. and Date
        doc.setFont("helvetica", "bold");
        doc.text("Sr. No.", x, y);
        doc.setTextColor(255, 0, 0);
        doc.text("USI-PO-" + formData.id, x + 35, y);
        doc.setTextColor(0, 0, 0);
        doc.text("Ref No:", x + 120, y);
        doc.setFont("helvetica", "normal");
        doc.text(formData.poNumber, x + 157, y);
        doc.setFont("helvetica", "bold");
        doc.text("Date:", x + 215, y);
        doc.setFont("helvetica", "normal");
        doc.text(formatDateUI(new Date(), false), x + 240, y);
        doc.line(originX, y + 7, originX + contentWidth, y + 7);

        // From/To Dept
        y += 25;

        doc.setFont("helvetica", "bold");
        doc.text("From Deptt:", x, y);
        doc.setFont("helvetica", "normal");
        const fromDepartmentText = formData.fromDepartment?.name?.toUpperCase() || "";
        if (fromDepartmentText.length <= 15) {
            doc.setFontSize(8);
        } else {
            doc.setFontSize(6);
        }
        doc.text(fromDepartmentText, x + 65, y);
        doc.setFontSize(10);
        doc.setFont("helvetica", "bold");
        doc.text("To Deptt:", x + 144, y);
        doc.setFont("helvetica", "normal");
        const toDepartmentText = formData.toDepartment?.name?.toUpperCase() || "";
        if (toDepartmentText.length <= 15) {
            doc.setFontSize(8);
        } else {
            doc.setFontSize(6);
        }
        doc.text(toDepartmentText, x + 190, y);
        doc.setFontSize(10);
        doc.line(originX, y + 7, originX + contentWidth, y + 7);

        // Name of Person
        y += 25;
        doc.setFont("helvetica", "bold");
        doc.text("Name of Person:", x, y);
        doc.setFont("helvetica", "normal");
        doc.setFontSize(10);
        const contactPerson = formData.supplierContactPerson || "";
        // const contactPerson = "Thsjmk SDfghju CGjhddopj HUJioddfgryiopju";
        if (contactPerson.length > 38) {
            doc.setFontSize(8);
        } else {
            doc.setFontSize(10);
        }
        doc.text(contactPerson, x + 83, y);
        doc.line(originX, y + 7, originX + contentWidth, y + 7);
        y += 25;
        doc.setFont("helvetica", "bold");
        doc.text("Supplier Name:", x, y);
        doc.setFont("helvetica", "normal");
        const supplierNameText = formData.supplier.name?.toUpperCase() || "";
        if (supplierNameText.length <= 38) {
            doc.setFontSize(10);
        } else {
            doc.setFontSize(8);
        }
        doc.text(capitalizeFirstWord(supplierNameText), x + 80, y);
        doc.line(originX, y + 7, originX + contentWidth, y + 7);

        // Table headers
        y += 7;
        // Make table full width - rate column extends to right edge
        const itemCodeWidth = 80;
        const qtyWidth = 60;
        const rateWidth = 70; // Fixed width for rate column
        const descriptionWidth = contentWidth - itemCodeWidth - qtyWidth - rateWidth; // Remaining space
        const colWidths = [itemCodeWidth, descriptionWidth, qtyWidth, rateWidth];
        const headers = ["Item Code", "Description", "Qty.", "Rate"];
        let colX = x;

        doc.setFont("helvetica", "bold");
        headers.forEach((text, i) => {
            const x = colX - 5;
            const yTop = y;
            const width = colWidths[i];
            const height = lineHeight + 10;

            // Top border
            doc.line(x, yTop, x + width, yTop);

            // Left border
            doc.line(x, yTop, x, yTop + height);

            // Right border - only draw for columns except the last one
            if (i < headers.length - 1) {
                doc.line(x + width, yTop, x + width, yTop + height);
            }
            doc.text(text, colX, y + 15);
            colX += colWidths[i];
        });

        // Table rows
        const MAX_LINES = 2;
        const MAX_ROWS = 5;
        const fixedRowHeight = lineHeight * MAX_LINES;

        let renderedRows = 0;

        const drawTableRow = (index: number, values: any[]) => {
            if (renderedRows >= MAX_ROWS) return;

            colX = x - 5;
            y += index != 1 ? fixedRowHeight : fixedRowHeight - 5;
            doc.setFont("helvetica", "normal");

            values.forEach((text: any, i: number) => {
                const str = text.toString();
                let lines = doc.splitTextToSize(str, colWidths[i] - 4);

                // Limit to 2 lines
                if (lines.length > MAX_LINES) {
                    lines = lines.slice(0, MAX_LINES);
                    lines[MAX_LINES - 1] += "...";
                }

                // Calculate vertical centering offset
                const totalTextHeight = lines.length * lineHeight;
                const offsetY = (fixedRowHeight - totalTextHeight) / 2;

                // Draw cell border - remove right border for last column
                if (i < colWidths.length - 1) {
                    doc.rect(colX, y, colWidths[i], fixedRowHeight);
                } else {
                    // Last column - draw borders without right border
                    doc.line(colX, y, colX + colWidths[i], y); // Top border
                    doc.line(colX, y, colX, y + fixedRowHeight); // Left border
                    doc.line(colX, y + fixedRowHeight, colX + colWidths[i], y + fixedRowHeight); // Bottom border
                }

                // Draw lines inside the cell
                lines.forEach((line: any, j: any) => {
                    doc.text(line, colX + 4, y + offsetY + 12 + j * lineHeight);
                });

                colX += colWidths[i];
            });

            renderedRows++;
        };

        const totalRows = 5;

        formData.items.forEach((item: any, index: number) => {
            drawTableRow(index + 1, [
                item.item.sku?.length <= 6 ? item.item.sku : "",
                item.item.name,
                item.qty,
                item.item.price,
            ]);
        });

        // Fill remaining rows with blanks
        const rowsToPad = totalRows - formData.items.length;

        for (let i = 0; i < rowsToPad; i++) {
            drawTableRow(2, ["", "", "", ""]);
        }
        //totals - positioned to align with columns
        y += fixedRowHeight;

        // Calculate column positions based on colWidths
        const totalTextColumnX = x - 5 + colWidths[0]; // Description column position for "Total:" text
        const qtyColumnX = x - 5 + colWidths[0] + colWidths[1]; // Item Code + Description columns
        const rateColumnX = x - 5 + colWidths[0] + colWidths[1] + colWidths[2]; // Item Code + Description + Qty columns

        // Add borders for all total cells (without top border)
        const totalCellHeight = 20;
        const totalCellY = y;

        // Borders for "Total:" text cell (left, bottom, right - no top)
        doc.line(totalTextColumnX, totalCellY, totalTextColumnX, totalCellY + totalCellHeight); // Left border
        doc.line(
            totalTextColumnX,
            totalCellY + totalCellHeight,
            totalTextColumnX + colWidths[1],
            totalCellY + totalCellHeight
        ); // Bottom border
        doc.line(
            totalTextColumnX + colWidths[1],
            totalCellY,
            totalTextColumnX + colWidths[1],
            totalCellY + totalCellHeight
        ); // Right border

        // Borders for qty total cell (left, bottom, right - no top)
        doc.line(qtyColumnX, totalCellY, qtyColumnX, totalCellY + totalCellHeight); // Left border
        doc.line(
            qtyColumnX,
            totalCellY + totalCellHeight,
            qtyColumnX + colWidths[2],
            totalCellY + totalCellHeight
        ); // Bottom border
        doc.line(
            qtyColumnX + colWidths[2],
            totalCellY,
            qtyColumnX + colWidths[2],
            totalCellY + totalCellHeight
        ); // Right border

        // Borders for rate total cell (left, bottom - no top, no right for last column)
        doc.line(rateColumnX, totalCellY, rateColumnX, totalCellY + totalCellHeight); // Left border
        doc.line(
            rateColumnX,
            totalCellY + totalCellHeight,
            rateColumnX + colWidths[3],
            totalCellY + totalCellHeight
        ); // Bottom border
        // No right border for the last column

        // Set font to bold for all total text
        doc.setFont("helvetica", "bold");

        // Calculate center Y position for text within the cell
        const centerY = totalCellY + totalCellHeight / 2 + 4; // +4 for text baseline adjustment

        // "Total:" text - aligned with Description column
        doc.text("Total:", totalTextColumnX + 4, centerY);

        // Qty total - aligned with Qty column
        doc.text(
            formData.items.reduce((acc, item) => acc + item.qty, 0).toString(),
            qtyColumnX + 4, // Add small padding like in table cells
            centerY
        );

        // Price total - aligned with Rate column
        doc.text(
            formData.items.reduce((acc, item) => acc + item.qty * item.item.price, 0).toString(),
            rateColumnX + 4, // Add small padding like in table cells
            centerY
        );

        // Reset font to normal
        doc.setFont("helvetica", "normal");

        // Footer

        doc.setFont("helvetica", "bold");
        doc.text("Required by Date:", x, contentHeight - 1);
        doc.text(formatDateUI(formData.expectedDate, false), x + 90, contentHeight - 1);
        doc.text("Auth Sig.:", x + 180, contentHeight - 1);
        doc.save(`Purchase-Order-${id}.pdf`);
    };
</script>

{#if isLoading}
    <PageLoader />
{:else}
    <div class="flex items-center justify-center">
        <div class="w-[90vw] p-2">
            <div class=" flex items-center justify-between py-2">
                <FormHeader label={"Demand Slip Item"}></FormHeader>
                <BreadCrumbs breadCrumbData={[]} />
            </div>

            <hr class="mb-5" />
            <div class=" grid grid-cols-3 gap-4 mt-5">
                <div>
                    <Label for="purchaseOrderNo" class="mb-2 font-sans capitalize tracking-[0px]">
                        Demand Slip No.
                    </Label>
                    <Input
                        type="text"
                        id="poNumber"
                        placeholder="PO No."
                        class="uppercase dark:bg-primary-700"
                        bind:value={formData.poNumber}
                        on:input={() => {
                            validationErrors = new Map();
                        }}
                    />
                    {#if validationErrors.has("poNumber")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("poNumber")}
                        </p>
                    {/if}
                </div>
                <div>
                    <SupplierSearch
                        selected={selectedSupplier}
                        onSelected={(data) => {
                            if (data) {
                                formData = { ...formData, id: data.id };
                                formData.supplier.id = data.id;
                                formData.supplier.name = data.name;
                                formData.items = [
                                    {
                                        item: {
                                            id: -1,
                                            name: "",
                                            gstPercentage: 0,
                                            price: 0,
                                            unitId: -1,
                                            unitName: "",
                                            categoryId: -1,
                                            categoryName: "",
                                            hsn: "",
                                            msq: 0,
                                            sku: "",
                                            status: RAW_MATERIAL_STAUS.ACTIVE,
                                            priceData: [],
                                            createdAt: new Date(),
                                            updatedAt: null,
                                            deletedAt: null,
                                            createdBy: "",
                                            updatedBy: null,
                                            deletedBy: null,
                                        },
                                        qty: 0,
                                    },
                                ];
                            } else {
                                formData.supplier.id = -1;
                                formData.items = [];
                            }
                            selectedSupplier = data;
                            validationErrors = new Map();
                        }}
                    />
                    {#if validationErrors.has("supplier")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("supplier")}
                        </p>
                    {/if}
                </div>
                <div>
                    <Label for="expectedData" class="mb-2 font-sans capitalize tracking-[0px]">
                        Expected Date
                    </Label>
                    <Input
                        type="date"
                        id="expectedData"
                        placeholder="Expected Data"
                        class="dark:bg-primary-700"
                        value={formatDateToInput(formData.expectedDate)}
                        onchange={(e: any) => {
                            formData.expectedDate = new Date(e.target.value);
                        }}
                    />
                    {#if validationErrors.has("expectedDate")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("expectedDate")}
                        </p>
                    {/if}
                </div>
            </div>

            <!-- Department and Contact Person Fields -->
            <div class=" grid grid-cols-3 gap-4 mt-5">
                <div>
                    <DepartmentSearch
                        labelText="From Department"
                        selected={selectedFromDepartment}
                        onSelected={(data) => {
                            selectedFromDepartment = data;
                            formData.fromDepartment = data;
                            validationErrors = new Map();
                        }}
                        excludeId={selectedToDepartment?.id}
                    />
                    {#if validationErrors.has("fromDepartment")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("fromDepartment")}
                        </p>
                    {/if}
                </div>
                <div>
                    <DepartmentSearch
                        labelText="To Department"
                        selected={selectedToDepartment}
                        onSelected={(data) => {
                            selectedToDepartment = data;
                            formData.toDepartment = data;
                            validationErrors = new Map();
                        }}
                        excludeId={selectedFromDepartment?.id}
                    />
                    {#if validationErrors.has("toDepartment")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("toDepartment")}
                        </p>
                    {/if}
                </div>
                <div>
                    <Label
                        for="supplierContactPerson"
                        class="mb-2 font-sans capitalize tracking-[0px]"
                    >
                        Supplier Contact Person
                    </Label>
                    <Input
                        type="text"
                        id="supplierContactPerson"
                        placeholder="Contact Person"
                        class="dark:bg-primary-700"
                        bind:value={formData.supplierContactPerson}
                        on:input={() => {
                            validationErrors = new Map();
                        }}
                    />
                    {#if validationErrors.has("supplierContactPerson")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("supplierContactPerson")}
                        </p>
                    {/if}
                </div>
            </div>

            <div class="m-2"></div>
            <span class="text-sm italic">Items</span>
            <div class="m-2"></div>
            <div class="overflow-x-auto">
                <table
                    class="w-full text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right border"
                >
                    <thead
                        class="bg-gray-50 font-primary text-base uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400"
                    >
                        <tr>
                            <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">SR No.</th>
                            <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">Item</th>
                            <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">
                                Total Qty
                            </th>
                            <th scope="col" class="px-6 py-3">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {#each formData.items as item, index}
                            <tr
                                class="border-b bg-white text-gray-600 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-600"
                            >
                                <td class="px-6 py-4">
                                    {index + 1}
                                </td>
                                <td class="px-6 py-4 w-[300px] h-[60px]">
                                    <div class="mb-[35px] w-[250px]">
                                        <SupplierRawMaterialSearch
                                            supplierId={formData.supplier.id}
                                            selected={getSelected(index)}
                                            onSelected={(data) => {
                                                onRawMaterialSelected(data, item, index);
                                            }}
                                        />
                                        {#if validationErrors.has("expectedDate")}
                                            <p
                                                class="pt-2 font-serif text-[14px] italic text-red-500"
                                            >
                                                {validationErrors.get("expectedDate")}
                                            </p>
                                        {/if}
                                    </div>
                                </td>

                                <td class="px-6 py-4">
                                    <Input
                                        class="mb-[35px] w-[100px]"
                                        type="number"
                                        value={item.qty}
                                        on:change={(e) => {
                                            handleTotalQty(e, index);
                                        }}
                                    />
                                </td>

                                <td class="px-6 py-4">
                                    <CloseButton
                                        class="w-content mb-[35px]"
                                        on:click={() => {
                                            const deletedItemId = formData.items[index].item.id;

                                            // Remove the item from the array (using slice for reactivity)
                                            formData.items = formData.items.filter(
                                                (item) => item.item.id !== deletedItemId
                                            );

                                            // Update selectedItemsMap based on IDs instead of indexes
                                            selectedItemsMap.delete(String(deletedItemId));

                                            // Update oldRawMaterialsIds correctly
                                            oldRawMaterialsIds = oldRawMaterialsIds.filter(
                                                (id) => id !== deletedItemId
                                            );
                                        }}
                                    />
                                </td>
                            </tr>
                        {/each}
                    </tbody>
                </table>
            </div>

            <div class="mt-5 flex w-full justify-between">
                <div>
                    {#if formData.items.length > 0}
                        <CustomButton
                            onClick={() => {
                                formData.items.push({
                                    item: {
                                        id: -1,
                                        name: "",
                                        gstPercentage: 0,
                                        price: 0,
                                        unitId: -1,
                                        unitName: "",
                                        categoryId: -1,
                                        categoryName: "",
                                        hsn: "",
                                        msq: 0,
                                        sku: "",
                                        status: RAW_MATERIAL_STAUS.ACTIVE,
                                        priceData: [],
                                        createdAt: new Date(),
                                        updatedAt: null,
                                        deletedAt: null,
                                        createdBy: "",
                                        updatedBy: null,
                                        deletedBy: null,
                                    },
                                    qty: 0,
                                });
                                formData = formData;
                            }}
                            cssClass=" bg-black"
                            title={"Add new item"}
                        />
                    {/if}
                </div>
                <div class="flex">
                    <CustomButton
                        onClick={onSubmitHandler}
                        cssClass="w-32 bg-black mr-2"
                        title={"Save"}
                    />
                    {#if isEditCase}
                        <CustomButton
                            onClick={() => {
                                generatePO(formData.id);
                            }}
                            cssClass="w-32 bg-black"
                            title={"Print"}
                        />
                    {/if}
                </div>
            </div>
        </div>
    </div>
{/if}

<!-- {#if isEditCase}
    <div
        id="print-stock-issuance"
        style="width:350px; margin:auto; font-family:sans-serif; border:1px solid #000;"
    >
       
        <div>
            <div
                style="font-size: 8px; font-weight: bold; text-align: right; text-transform: uppercase; padding: 10px 10px 0 0;"
            >
                Created By: {$loggedInUser?.firstName}
                {$loggedInUser?.lastName}
            </div>
        </div>

        <div>
            <div style="text-align: center; padding: 5px;">
                <img
                    src="/images/logo.png"
                    alt="logo"
                    style="height: 30px; display: block; margin: 0 auto;"
                />
            </div>
        </div>
        <table style="width:100%; border-collapse:collapse; font-size:13px;">
            <tbody>
                <tr>
                    <td style="width:60%; border-bottom:1px solid #000; padding:2px;">
                        Sr. No:
                        <span style="color:red; font-weight:bold; padding-left: 5px;">
                            USI-PO-{formData.id}
                        </span>
                    </td>
                    <td style="border-bottom:1px solid #000; padding:2px;">
                        Date:
                        <span style="font-weight:bold; padding-left: 5px;">
                            {formatDateUI(new Date(), false)}
                        </span>
                    </td>
                </tr>
                <tr>
                    <td style="border-bottom:1px solid #000; padding:2px;">From Deptt:</td>
                    <td style="border-bottom:1px solid #000; padding:2px;">To Deptt:</td>
                </tr>
                <tr>
                    <td colspan="2" style="border-bottom:1px solid #000; padding:2px;">
                        Name of Person:
                    </td>
                </tr>
                <tr>
                    <td colspan="2" style=" padding:2px;">
                        Name of Party:
                        <span style="font-weight:bold; padding-left: 5px;">
                            {formData.supplier.name}
                        </span>
                    </td>
                </tr>
            </tbody>
        </table>
        <table style="width:100%; border-collapse:collapse; font-size:13px;">
            <thead>
                <tr>
                    <th style="border:1px solid #000; padding:2px; width:18%;">Item Code</th>
                    <th style="border:1px solid #000; padding:2px;">Description</th>
                    <th style="border:1px solid #000; padding:2px; width:15%;">Qty.</th>
                    <th style="border:1px solid #000; padding:2px; width:15%;">Rate</th>
                </tr>
            </thead>
            <tbody style="min-height: 200px; margin:auto;">
                {#each formData.items as item, index}
                    <tr>
                        <td style="border:1px solid #000; margin:auto; text-align:center;">
                            {index + 1}
                        </td>
                        <td style="border:1px solid #000; margin:auto; text-align:center;">
                            {item.item.name}
                        </td>
                        <td style="border:1px solid #000; margin:auto; text-align:center;">
                            {item.qty}
                        </td>
                        <td style="border:1px solid #000; margin:auto; text-align:center;">
                            {item.item.price}
                        </td>
                    </tr>
                {/each}
                {#each Array(Math.max(0, 4 - formData.items.length)) as _, i}
                    <tr>
                        <td style="border:1px solid #000; padding:8px 8px;"></td>
                        <td style="border:1px solid #000;"></td>
                        <td style="border:1px solid #000;"></td>
                        <td style="border:1px solid #000;"></td>
                    </tr>
                {/each}
            </tbody>
        </table>
        <table style="width:100%; border-collapse:collapse; font-size:13px; margin-top:8px;">
            <tbody>
                <tr>
                    <td style="width:60%; padding:2px;">Required by Date:</td>
                    <td style="padding:2px;">Auth Sig.:</td>
                </tr>
            </tbody>
        </table>
    </div>

    <style>
        #print-stock-issuance {
            /* visibility: hidden; */
        }
    </style>
{/if} -->
