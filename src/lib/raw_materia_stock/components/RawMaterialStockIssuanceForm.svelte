<script lang="ts">
    import { goto } from "$app/navigation";
    import CustomButton from "$lib/common/components/admin/CustomButton.svelte";
    import { formatDateUI, showErrorToast, showSuccessToast } from "$lib/common/utils/common-utils";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { Label, Input, CloseButton, Textarea } from "flowbite-svelte";
    import FormHeader from "$lib/common/components/admin/FormHeader.svelte";
    import BreadCrumbs from "$lib/common/components/admin/BreadCrumbs.svelte";
    import type { IRawMaterialStockIssuance, RawMaterials } from "../models/IRawMaterialStock";
    import SupplierRawMaterialSearch from "$lib/purchase_invoice/components/SupplierRawMaterialSearch.svelte";
    import type { IRawMaterial } from "$lib/raw_material/models/IRawMaterial";
    import UsersDropdown from "$lib/users/components/admin/UsersDropdown.svelte";
    import type { IUser } from "$lib/users/models/User";
    import { afterUpdate, onMount } from "svelte";
    import Loading from "$lib/common/components/Loading.svelte";
    import { validateStockIssuanceSchema } from "../utils/RawMaterialStockUtils";
    import jsPDF from "jspdf";

    export let formData: IRawMaterialStockIssuance;
    export let isDetailPage: boolean = false;
    export let isEditMode: boolean = false;
    export let toggleEditMode: (() => void) | null = null;

    let isDoingTask: boolean = false;
    let users: IUser[] = [];
    let selectedItemsMap: Map<string, RawMaterials> = new Map();
    let isLoadingData = {
        index: 0,
        loading: false,
    };
    let notesValidation: boolean = false;
    let validationErrors: Map<string, string> = new Map();
    let itemValidationErrors: Map<string, string> = new Map();
    let isLoadingUsers: boolean = true;
    let stockMap = new Map<string, { totalStock: number; usableStock: number }>();
    let existingItemsSet = new Set<string>(); // Track existing items in edit mode
    const getSelected = (key: string) => {
        const item = selectedItemsMap.get(key);
        return item && item.name.trim() !== ""
            ? ({
                  id: item.rawMaterialId,
                  name: item.name,
                  qty: item.qty,
              } as unknown as IRawMaterial)
            : null;
    };

    const onRawMaterialSelected = (data: IRawMaterial, index: number) => {
        if (!data) {
            formData.rawMaterials[index] = {
                rawMaterialId: formData.rawMaterials[index].rawMaterialId,
                qty: formData.rawMaterials[index].qty,
                name: "",
            };
        } else {
            selectedItemsMap.set(data.name, {
                rawMaterialId: data.id,
                qty: 0,
                name: data.name,
            });
            selectedItemsMap.delete("");

            // In edit mode, only fetch stock for new items (not existing ones)
            const shouldFetchStock = !isEditMode || !existingItemsSet.has(data.name);
            if (shouldFetchStock) {
                getCurrentStock(data.id, index);
            }
        }
        formData = formData;
        selectedItemsMap = selectedItemsMap;
    };

    const validateNotes = (input: string) => {
        const words = input.trim().split(/\s+/);
        if (words.length < 3) {
            notesValidation = true;
            formData.notes = "";
        } else {
            notesValidation = false;
            formData.notes = input; // Set valid input
        }
    };

    const handleQty = (e: any, key: string) => {
        try {
            let newQty = parseFloat(e.currentTarget.value.trim());

            // In edit mode, only enforce stock limits for new items (not existing ones)
            const isExistingItem = isEditMode && existingItemsSet.has(key);
            const usableStock = stockMap.get(key)?.usableStock ?? 0;

            if (!isExistingItem && newQty > usableStock) {
                // Only limit to stock for new items or when not in edit mode
                newQty = usableStock;
            } else if (newQty < 0 || isNaN(newQty)) {
                newQty = 0;
            }

            selectedItemsMap.set(key, {
                ...selectedItemsMap.get(key),
                qty: newQty,
            } as RawMaterials);

            selectedItemsMap = new Map(selectedItemsMap); // Force reactivity
        } catch (error) {
            console.error("Error updating qty:", error);
        }
    };

    const getCurrentStock = async (id: number, index: number) => {
        isLoadingData = {
            index: index,
            loading: true,
        };
        const result = await PresenterProvider.rawMaterialStockPresenter.getByRawMaterialId(id);

        if (!result.success) {
            showErrorToast(result.message);
        } else {
            stockMap.set(result.data.rawMaterialName, result.data);
            stockMap = stockMap;
        }
        isLoadingData = {
            index: index,
            loading: false,
        };
    };

    const _loadUsers = async () => {
        const response = await PresenterProvider.userPresenter.getAll(1, 1000);
        if (!response.success) {
            showErrorToast(response.message);
        } else {
            users = response.data.data;
            if (!isDetailPage) {
                formData.issuedToUserId = users[0].coreUserId;
            }
            isLoadingUsers = false;
        }
    };
    const handleSubmit = async () => {
        formData.rawMaterials = Array.from(selectedItemsMap.values());
        formData.soNumber = formData.soNumber.trim().toLowerCase();
        formData = formData;

        validationErrors =
            PresenterProvider.rawMaterialStockPresenter.validateStockIssuance(formData);
        itemValidationErrors = validateStockIssuanceSchema(formData.rawMaterials);
        itemValidationErrors = new Map([
            ...itemValidationErrors,
            ...validateStockIssuanceSchema(formData.rawMaterials),
        ]);

        if (validationErrors.size !== 0 && itemValidationErrors.size !== 0) {
            showErrorToast("Please fill the required fields correctly");
            return;
        }
        isDoingTask = true;
        let res;
        if (isEditMode) {
            res = await PresenterProvider.rawMaterialStockPresenter.editStockIssuance(formData);
        } else {
            res = await PresenterProvider.rawMaterialStockPresenter.issueStock(formData);
        }
        if (res.success) {
            showSuccessToast(`Done`);
            await goto("/admin/raw-materials-stock/issuance");
        } else {
            showErrorToast(res.message);
            isDoingTask = false;
        }
    };

    const print = () => {
        isDoingTask = true;
        const printElement = document.getElementById("print-stock-issuance");
        if (printElement) {
            printElement.style.visibility = "visible";
            const doc = new jsPDF("p", "pt", "a4");
            const pageWidth = doc.internal.pageSize.getWidth();
            const pageHeight = doc.internal.pageSize.getHeight();
            const printElementHeightPx = printElement.offsetHeight;
            const contentWidth = 216;
            const contentHeightPt = printElementHeightPx * 0.75;
            const xOffset = (pageWidth - contentWidth) / 2.5;
            // const yOffset = (pageHeight - contentHeightPt) / 2.5;
            doc.html(printElement, {
                x: xOffset,
                // y: yOffset,
                callback: function (doc) {
                    doc.save("Stock Issuance  " + formData.soNumber.toUpperCase() + ".pdf");
                    setTimeout(() => {
                        printElement.style.visibility = "hidden";
                        isDoingTask = false;
                    }, 600);
                },
            });
        }
    };

    onMount(() => {
        _loadUsers();
        selectedItemsMap.set("", {
            rawMaterialId: 0,
            name: "",
            qty: 0,
        });

        if (isDetailPage) {
            selectedItemsMap.clear();
            let index = 0;
            for (const element of formData.rawMaterials) {
                // Track existing items for edit mode logic
                existingItemsSet.add(element.name);

                // In edit mode, don't fetch stock for existing items
                if (!isEditMode) {
                    getCurrentStock(element.rawMaterialId, index);
                }

                selectedItemsMap.set(element.name, element);
                index++;
            }
        }
        selectedItemsMap = selectedItemsMap;
    });

    $: {
        selectedItemsMap = selectedItemsMap;
    }
</script>

<div class=" flex items-center justify-center">
    <div class=" w-[90vw] p-2">
        <div class="flex items-center justify-between py-2">
            <div class="flex items-center gap-4">
                <FormHeader label={isDetailPage ? "Stock Issuance Details" : "Issue Stock"}
                ></FormHeader>
                {#if isEditMode}
                    <span
                        class="bg-orange-100 text-orange-800 text-sm font-medium px-2.5 py-0.5 rounded dark:bg-orange-900 dark:text-orange-300"
                    >
                        Edit Mode
                    </span>
                {/if}
            </div>
            <div class="flex items-center gap-4">
                {#if isDetailPage && toggleEditMode}
                    <CustomButton
                        onClick={toggleEditMode}
                        cssClass="w-32 {isEditMode ? 'bg-gray-600' : 'bg-blue-600'}"
                        title={isEditMode ? "Cancel Edit" : "Edit"}
                    />
                {/if}
                <BreadCrumbs breadCrumbData={[]} />
            </div>
        </div>
        <hr class="mb-5" />
        <div class=" grid grid-cols-3 gap-6">
            <div>
                <Label for="soNumber" class="mb-2 font-sans capitalize tracking-[0px]">
                    Sales Order Number
                    {#if validationErrors.has("soNumber")}
                        <span class="text-red-600">*</span>
                    {/if}
                </Label>

                <Input
                    disabled={isDetailPage && !isEditMode}
                    id="soNumber"
                    class="uppercase dark:bg-primary-700 {validationErrors.has('soNumber')
                        ? 'border-red-500'
                        : ''}"
                    bind:value={formData.soNumber}
                    on:input={() => (validationErrors = new Map())}
                />

                {#if validationErrors.has("soNumber")}
                    <p class="pt-2 font-serif text-[14px] italic text-red-500">
                        {validationErrors.get("soNumber")}
                    </p>
                {/if}
            </div>
            <div>
                <Label for="purchasedById" class="mb-2 font-sans capitalize tracking-[0px]">
                    Issued To
                    {#if validationErrors.has("purchasedById")}
                        <span class="text-red-600">*</span>
                    {/if}
                </Label>

                <UsersDropdown
                    disabled={isDetailPage && !isEditMode}
                    data={users}
                    id="purchasedById"
                    cssClass="dark:bg-primary-700 {validationErrors.has('issuedToUserId')
                        ? 'border-red-500'
                        : ''}"
                    selectedValue={formData.issuedToUserId}
                    onSelected={(data) => {
                        formData.issuedToUserId = Number(data.toString());
                    }}
                />

                {#if validationErrors.has("issuedToUserId")}
                    <p class="pt-2 font-serif text-[14px] italic text-red-500">
                        {validationErrors.get("issuedToUserId")}
                    </p>
                {/if}
            </div>
        </div>
        <div class="m-2"></div>
        <hr class="mb-5" />
        <table
            class="w-full text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right border"
        >
            <thead
                class="bg-gray-50 font-primary text-base uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400"
            >
                <tr>
                    <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">SR No.</th>
                    <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">Item</th>
                    {#if !isDetailPage || isEditMode}
                        <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">Total Stock</th>
                        <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">
                            Usable Stock
                        </th>
                    {/if}
                    <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">Issued Qty</th>
                    <th scope="col" class="px-6 py-3">Action</th>
                </tr>
            </thead>
            <tbody>
                {#each Array.from(selectedItemsMap) as [key, value], index}
                    <tr
                        class="border-b bg-white text-gray-600 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-600"
                    >
                        <td class="px-6 py-4">
                            {index + 1}.
                        </td>
                        <td class="px-6 py-4">
                            <SupplierRawMaterialSearch
                                disabled={isDetailPage && !isEditMode}
                                showAddNew={false}
                                selected={getSelected(key)}
                                onSelected={(data) => {
                                    itemValidationErrors = new Map();
                                    onRawMaterialSelected(data, index);
                                }}
                                onCrossClick={() => {
                                    const item = selectedItemsMap.get(key);
                                    if (item) {
                                        selectedItemsMap.set("", {
                                            qty: 0,
                                            rawMaterialId: 0,
                                            name: "",
                                        });

                                        stockMap.delete(value.name);
                                        stockMap = stockMap;
                                    }
                                    selectedItemsMap.delete(key);
                                }}
                            />
                            {#if itemValidationErrors.has("rawMaterialId" + `${index}`)}
                                <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                    {itemValidationErrors.get("rawMaterialId" + `${index}`)}
                                </p>
                            {/if}
                        </td>
                        {#if !isDetailPage || isEditMode}
                            <td class="px-6 py-4">
                                {#if isLoadingData.loading && isLoadingData.index === index}
                                    <div class="flex justify-center items-center">
                                        <Loading />
                                    </div>
                                {:else}
                                    <Input
                                        disabled
                                        type="number"
                                        id="totalStock"
                                        class="dark:bg-primary-700"
                                        value={stockMap.get(value.name)?.totalStock ?? 0}
                                    />
                                {/if}
                            </td>
                            <td class="px-6 py-4">
                                {#if isLoadingData.loading && isLoadingData.index === index}
                                    <div class="flex justify-center items-center">
                                        <Loading />
                                    </div>
                                {:else}
                                    <Input
                                        disabled
                                        type="number"
                                        id="usableStock"
                                        class="dark:bg-primary-700"
                                        value={stockMap.get(value.name)?.usableStock ?? 0}
                                    />
                                {/if}
                            </td>
                        {/if}
                        <td class="px-6 py-4">
                            <Input
                                disabled={isDetailPage && !isEditMode}
                                type="number"
                                id="qty"
                                class="dark:bg-primary-700 {validationErrors.has('qty')
                                    ? 'border-red-500'
                                    : ''}"
                                value={value.qty}
                                min="0"
                                max={isEditMode && existingItemsSet.has(key) ? undefined : (stockMap.get(value.name)?.usableStock ?? 0)}
                                on:input={(e) => {
                                    handleQty(e, key);
                                    itemValidationErrors = new Map();
                                }}
                            />

                            {#if itemValidationErrors.has("qty" + `${index}`)}
                                <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                    {itemValidationErrors.get("qty" + `${index}`)}
                                </p>
                            {/if}
                        </td>
                        <td class="px-6 py-4">
                            <CloseButton
                                class="w-content "
                                on:click={() => {
                                    selectedItemsMap.delete(key);
                                    selectedItemsMap = selectedItemsMap;
                                }}
                                disabled={isDetailPage && !isEditMode}
                            />
                        </td>
                    </tr>
                {/each}
            </tbody>
        </table>

        {#if !isDetailPage || isEditMode}
            <CustomButton
                onClick={() => {
                    selectedItemsMap.set("", {
                        rawMaterialId: selectedItemsMap.size + 1,
                        qty: 0,
                        name: "",
                    });

                    selectedItemsMap = selectedItemsMap;
                }}
                cssClass=" bg-black mt-4"
                title={"Add New Item"}
                isLoading={isDoingTask}
            />
        {/if}
        <div class="w-full mt-4">
            <Label for="soNumber" class="mb-2 font-sans capitalize tracking-[0px]">Notes</Label>
            <Textarea
                disabled={isDetailPage && !isEditMode}
                placeholder="Write meaningful notes with at least 3 words..."
                value={formData.notes!}
                on:input={(e) =>
                    //@ts-ignore
                    validateNotes(e.currentTarget.value)}
            ></Textarea>
            {#if notesValidation}
                <p class="pt-2 font-serif text-[14px] italic text-red-500">
                    Write atleast 3-4 words!
                </p>
            {/if}
        </div>

        <!-- Buttons should be outside the table -->
        <div class="mt-5 flex w-full justify-end gap-4">
                <CustomButton
                    onClick={handleSubmit}
                    cssClass="w-32 bg-black"
                    title={isEditMode ? "Update" : "Save"}
                    isLoading={isDoingTask}
                />
            {#if isDetailPage}
                <CustomButton
                    onClick={() => {
                        print();
                    }}
                    cssClass="w-32 bg-black"
                    title={"Print"}
                />
            {/if}
        </div>
    </div>
</div>

{#if isDetailPage}
    <div
        id="print-stock-issuance"
        style="
width:216pt;
max-width: 216pt;
font-size: 8px;
"
    >
        <div style="margin-bottom: 10px;;">
            <img
                src="/images/logo.png"
                alt="logo"
                style="width:100px;height:30px;margin-left:auto;margin-right:auto;"
            />
        </div>
        <div class="w-full mb-[1rem] text-center">
            <h1 class="text-[0.4rem] font-bold">Stock Issuance #{formData.entryId}</h1>
        </div>

        <div style="font-size: 6px;">
            <div style="margin-bottom: 0px;">
                <span style=" font-weight: bold;margin-right: 5px;">Date:</span>
                {formatDateUI(formData.issuedAt)}
            </div>
            <div style="margin-bottom: 0px;">
                <span style=" font-weight: bold;margin-right: 5px;">Issued To:</span>
                {formData.issuedTo?.firstName.toUpperCase() ?? "N/A"}
            </div>

            <div style="margin-bottom: 0px;">
                <span style=" font-weight: bold;margin-right: 5px;">Issued By:</span>
                {formData.issuedBy.firstName.toUpperCase()}
            </div>
        </div>
        <div
            style=" margin-top: 10px;
width: 100%;"
        >
            <table
                id="print-table"
                style=" 
font-size: 5px;
width: 100%;
border-collapse: collapse;
border: 1px solid #ddd;"
            >
                <thead>
                    <tr>
                        <th
                            class="whitespace-nowrap"
                            style="
        border: 1px solid #ddd;
        padding: 4px;
        text-align: left;
        vertical-align: middle;
        background-color: #f2f2f2;
        font-weight: bold;"
                        >
                            Sr. No.
                        </th>
                        <th
                            class="whitespace-nowrap"
                            style="
    border: 1px solid #ddd;
    padding: 4px;
    text-align: left;
    vertical-align: middle;
    background-color: #f2f2f2;
    font-weight: bold;"
                        >
                            Item
                        </th>
                        <th
                            class="whitespace-nowrap"
                            style="
border: 1px solid #ddd;
padding: 4px;
        text-align: left;
        vertical-align: middle;
         background-color: #f2f2f2;
        font-weight: bold;"
                        >
                            Qty
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {#each formData.rawMaterials as row, index}
                        <tr>
                            <td
                                style="border: 1px solid #ddd; padding: 4px; text-align: left; vertical-align: middle;"
                            >
                                {index + 1}
                            </td>
                            <td
                                style="border: 1px solid #ddd; padding: 4px; text-align: left; vertical-align: middle;"
                            >
                                {row.name.toUpperCase()}
                            </td>
                            <td
                                style="border: 1px solid #ddd; padding: 4px; text-align: left; vertical-align: middle;"
                            >
                                {row.qty}
                            </td>
                        </tr>
                    {/each}
                </tbody>
            </table>

            <div style="margin-top: 10px; font-size: 5px;">
                Authorized Signature __________________
            </div>
        </div>
    </div>

    <style>
        #print-stock-issuance {
            visibility: hidden;
        }
    </style>
{/if}
