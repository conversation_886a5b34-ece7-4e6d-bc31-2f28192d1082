import { InterfaceMetaData } from "../../../core/CoreInterfaces";
import { DEPARTMENT_STATUS } from "./DepartmentMisc";

interface IDepartment extends InterfaceMetaData {
    // id: number;
    name: string;
    status: DEPARTMENT_STATUS;
}

interface ICreateDepartment extends InterfaceMetaData {
    name: string;
    status: DEPARTMENT_STATUS;
}

interface IUpdateDepartment extends Omit<ICreateDepartment, "createdById"> {
    id: bigint;
    updatedById: bigint;
}

interface IDepartmentResponse {
    id: bigint;
    name: string;
    status: DEPARTMENT_STATUS;
    createdBy: string;
    createdAt: Date;
}


export { IDepartment, ICreateDepartment, IUpdateDepartment, IDepartmentResponse };
